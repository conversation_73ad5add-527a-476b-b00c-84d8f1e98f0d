use std::fmt::{Disp<PERSON>, Formatter};

#[derive(Clone, Debug, PartialEq, Eq, Hash)]
pub(crate) enum InnerParseError {
    Pest(crate::query::Error),
    Other(DetachedSpan, String),
}

impl Display for InnerParseError {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        match &self {
            InnerParseError::Pest(error) => Display::fmt(error, f),
            InnerParseError::Other(_, message) => Display::fmt(message, f),
        }
    }
}

impl std::error::Error for InnerParseError {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        match self {
            InnerParseError::Pest(err) => Some(err),
            InnerParseError::Other(_, _) => None,
        }
    }
}

impl From<crate::query::Error> for InnerParseError {
    fn from(err: crate::query::Error) -> Self {
        Self::Pest(err)
    }
}

/// Like a [pest::Span], but without a reference to the underlying `&str`, and thus cheaply Copyable.
#[derive(Copy, Clone, Default, Debug, PartialEq, Eq, Hash)]
pub(crate) struct DetachedSpan {
    pub(crate) start: usize,
    pub(crate) end: usize,
}

impl From<pest::Span<'_>> for DetachedSpan {
    fn from(value: pest::Span) -> Self {
        Self {
            start: value.start(),
            end: value.end(),
        }
    }
}

impl From<&crate::query::Pair<'_>> for DetachedSpan {
    fn from(value: &crate::query::Pair<'_>) -> Self {
        value.as_span().into()
    }
}
